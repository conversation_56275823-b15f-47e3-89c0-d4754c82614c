import React, { useEffect } from "react";
import "./sidebarStyles.scss";
import { connect } from "react-redux";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
// import IconButton from "@mui/material/IconButton";
import { ExitToAppRounded } from "@mui/icons-material";
import NotificationsIcon from "@mui/icons-material/Notifications";

import { logoutUser } from "../../../library/common/actions/AuthActions";
import { Link } from "react-router-dom";
import { Badge, Box, IconButton, Tooltip, Typography } from "@mui/material";
import { setReviewCount } from "../../../store/common/actions/review.actions";
import { setContactCount } from "../../../store/common/actions/contact.actions";
import axiosInstance from "../../../helpers/Axios";
import logo from "../../../images/SmartB_Logo.svg";

const DefaultHeader = ({
  user,
  logoutUser,
  reviews,
  setReviewCount,
  setContactCount,
  contacts,
}) => {
  // const classes = useStyles();
  var loginuser = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : JSON.parse(localStorage.getItem("user"));

  const handleLogout = () => {
    logoutUser();
  };

  useEffect(() => {
    fetchReviewCount();
    fetchContactCount();
  }, []);

  const fetchReviewCount = async () => {
    try {
      const { status, data } = await axiosInstance.get(`/reviews/count`);
      if (status === 200) {
        setReviewCount(data?.result);
      } else {
      }
    } catch (err) {}
  };

  const fetchContactCount = async () => {
    try {
      const { status, data } = await axiosInstance.get(`/contact/count/unread`);
      if (status === 200) {
        setContactCount(data?.result);
      } else {
      }
    } catch (err) {}
  };

  return (
    <div className="headerWrapper">
      <AppBar position="static" className="bg-green top-bar">
        <Toolbar variant="dense" className="header">
          {/* <IconButton edge="start" color="inherit" aria-label="menu">
            <MenuIcon />
          </IconButton> */}
          <Link to="/dashboard">
            <img src={logo} alt="smartb logo" />
          </Link>

          <div className="header-user">
            <span className="notification-icon">
              <Tooltip
                title={
                  <Box>
                    <Typography>
                      <Link
                        to={
                          loginuser?.role === "admin"
                            ? "/bookkeeper-review"
                            : "/dashboard"
                        }
                      >
                        <Typography className="mb-6">
                          BookKeeper Review :{" "}
                          <span className="red">{reviews?.unread}</span>
                        </Typography>
                      </Link>
                    </Typography>
                    <Typography>
                      <Link
                        to={
                          loginuser?.role === "admin"
                            ? "/contact-us"
                            : "/dashboard"
                        }
                      >
                        <Typography>
                          Contact Count :{" "}
                          <span className="red">{contacts?.unread}</span>
                        </Typography>
                      </Link>
                    </Typography>
                  </Box>
                }
                interactive
                placement="bottom-end"
                classes={{
                  tooltip: "notification-tooltip-wrap",
                }}
              >
                <IconButton>
                  <Badge
                    badgeContent={reviews?.unread + contacts?.unread}
                    color="secondary"
                  >
                    <NotificationsIcon />
                  </Badge>
                </IconButton>
              </Tooltip>
            </span>

            <button
              className="go-to-main-btn"
              onClick={() => (window.location.href = "/")}
            >
              Go To Main Site
            </button>
            <p className="loginuser_name">{loginuser && loginuser?.username}</p>
            <button
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              color="inherit"
              className="logout-icon"
              onClick={handleLogout}
            >
              <ExitToAppRounded />
            </button>
          </div>
        </Toolbar>
      </AppBar>
    </div>
  );
};

const mapStateToProps = ({ authReducer, reviewReducer, contactReducer }) => {
  return {
    user: authReducer.user,
    reviews: reviewReducer.reviews,
    contacts: contactReducer.contacts,
  };
};

export default connect(mapStateToProps, {
  logoutUser,
  setReviewCount,
  setContactCount,
})(DefaultHeader);
