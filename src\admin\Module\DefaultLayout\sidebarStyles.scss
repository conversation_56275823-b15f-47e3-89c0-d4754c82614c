.sidebar {
  width: 100%;
  font-size: 14px;
  transition: all 0.5s;
  background-color: #003764;
  height: calc(100vh - 56px);

  a {
    color: #fff;
  }

  .nav-item {
    padding: 10px 15px;
    transition: all 0.5s;

    &:hover {
      background-color: #63c2de;
      color: #ffffff;
    }
  }

  .active {
    .nav-item {
      background-color: #3a4248;
      color: #63c2de;
    }
  }
}

.menu-item {
  padding: 3px 5px;
}

.changePassword {
  cursor: pointer;
  padding: 3px 5px;

  &:hover {
    color: #fff;
    background-color: #435863;
    border-radius: 3px;
  }

  a {
    &:hover {
      color: #fff;
      background-color: #435863;
      border-radius: 3px;
    }
  }
}

.sidemenu {
  .MuiPaper-root {
    height: 55px;
    justify-content: center;
  }

  .MuiToolbar-root {
    justify-content: space-between;
  }
}

.page-contentWrapper {
  text-align: left;
  //padding: 15px;
}

.pageWrapper {
  min-height: 80vh;
  padding: 10px 15px;

  .addButton {
    float: right;
    position: relative;
    top: 5px;
  }
}

.modalTable {
  padding: 0 !important;
}

.mainLayout {
  .MuiGrid-container {
    background: #f7f7f7;
  }

  .headerWrapper {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;

    .header {
      min-height: 64px;

      img {
        max-width: 108px;
      }
    }

    .MuiToolbar-gutters {
      display: flex;
      justify-content: space-between;
    }

    .go-to-main-btn {
      height: 30px;
      margin: 12px;
      color: #fff;
      background-color: #4455c7;
      border: none;
      border-radius: 5px;
      font-size: 13px;
      padding: 0 15px;
      cursor: pointer;
    }
  }

  .sidebar {
    position: fixed;
    width: 14%;
    top: 65px;
    //height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    // &::-webkit-scrollbar-track {
    //   background: #3f5965;
    // }

    &::-webkit-scrollbar-thumb {
      background: #003764;
    }

    @media (max-width: 1460px) {
      width: 17%;
    }
  }

  .pageWrapper {
    margin-top: 50px;
  }

  button {
    min-width: 80px;
  }
}

.loginuser_name {
  color: #003764;
}

.notification-icon {
  .MuiButtonBase-root {
    padding: 0px;
    min-width: auto;
  }
}

.notification-tooltip-wrap {
  background-color: #FFFFFF !important;
  border-radius: 8px !important;
  color: #003764 !important;
  box-shadow: 3px 6px 14px 0px #00000033;
  padding: 10px !important;
  margin-top: 5px !important;

  p {
    font-size: 16px;
    line-height: 19px;
    font-weight: 400;

  }

  .mb-6 {
    margin-bottom: 6px;
  }

  .red {
    color: red;
  }
}