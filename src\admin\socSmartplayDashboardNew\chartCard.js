import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import EastIcon from "@mui/icons-material/East";
import WestIcon from "@mui/icons-material/West";
import Select from "react-select";
import { Box, Button, Typography } from "@mui/material";
import UpPrice from "../../images/upArrowPrice.png";
import moment from "moment";

const generateUniqueId = () => {
  return "_" + Math.random().toString(36).substr(2, 9);
};

// Replace with your actual months array if not defined globally
const MONTHS = [
  { label: "January", value: 1 },
  { label: "February", value: 2 },
  { label: "March", value: 3 },
  { label: "April", value: 4 },
  { label: "May", value: 5 },
  { label: "June", value: 6 },
  { label: "July", value: 7 },
  { label: "August", value: 8 },
  { label: "September", value: 9 },
  { label: "October", value: 10 },
  { label: "November", value: 11 },
  { label: "December", value: 12 },
];

const CustomTooltip = ({ active, payload, label }) => {
  console.log("object1234566456", active, payload, label);

  if (active && payload?.length) {
    return (
      <div className="custom-tooltip">
        {payload.map((item) => (
          <>
            <Box className="tooltip-header">
              <p className="tooltip-title">{`${item?.name}`}</p>
              <p className="tooltip-title">
                <b>{item?.value}</b>
              </p>
            </Box>
            <p key={generateUniqueId()} className="tooltip-item">
              {moment(item?.payload?.date).format("ddd, MMM D, YYYY")}
            </p>
          </>
        ))}
      </div>
    );
  }

  return null;
};

const CustomLegend = ({ payload }) => {
  return (
    <div className="custom-legend">
      {payload?.map((entry, index) => (
        <div key={index} className="legend-item">
          <span
            className="legend-color"
            style={{ backgroundColor: entry?.color }}
          ></span>
          <span className="legend-label">{entry?.value}</span>
        </div>
      ))}
    </div>
  );
};

const ChartCard = ({
  title,
  value,
  change,
  data,
  dataKeys,
  colors,
  hasSpecialFilter = false,
  specialFilterValue = "Last 7 days",
  onSpecialFilterChange = () => {},
  hasWeekNav = true,
  weekIdx = 0,
  onWeekChange = () => {},
  maxWeeks = 1,
  selectedMonth = 1,
  onMonthChange = () => {},
  prefix = "",
  selectedYear = null,
  barSize,
}) => {
  const currentDate = new Date();

  const availableMonths = MONTHS.filter((m) =>
    currentDate.getFullYear() === selectedYear
      ? m.value <= currentDate.getMonth() + 1
      : true
  );

  // Create special filter options
  const specialFilterOptions = [
    { label: "Last 7 days", value: "Last 7 days" },
    { label: "Last 30 days", value: "Last 30 days" },
  ];

  return (
    <Box>
      {/* Header */}
      <Box className="d-flex-ss align-items-center justify-content-between">
        <Typography className="graph-title">{title}</Typography>
        <Box>
          {hasSpecialFilter ? (
            <Select
              className="React cricket-select external-select w-100"
              classNamePrefix="select"
              placeholder="Select Filter"
              options={specialFilterOptions}
              value={specialFilterOptions.find(
                (option) => option.value === specialFilterValue
              )}
              onChange={(option) => onSpecialFilterChange(option?.value)}
              menuPosition="fixed"
            />
          ) : (
            <Select
              className="React cricket-select external-select w-100"
              classNamePrefix="select"
              placeholder="Select Month"
              options={availableMonths}
              value={availableMonths.find(
                (month) => month.value === selectedMonth
              )}
              onChange={(option) => onMonthChange(option?.value)}
              menuPosition="fixed"
            />
          )}
        </Box>
      </Box>
      <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
        <Typography className="details-count">
          {" "}
          {prefix}
          {value}
        </Typography>
        <Typography className="value-change-count d-flex-ss align-items-center ">
          <img src={UpPrice} alt="icon" /> {change}%
        </Typography>
      </Box>
      <Box className="mt-18">
        {hasWeekNav && (
          <Box className="d-flex-ss align-items-center justify-content-between">
            <Button
              className="d-flex-ss align-items-center arrow-text"
              disabled={weekIdx === 0}
              onClick={() => onWeekChange(weekIdx - 1)}
            >
              <WestIcon />
              Previous Week
            </Button>
            <Button
              className="d-flex-ss align-items-center arrow-text"
              disabled={weekIdx >= maxWeeks - 1}
              onClick={() => onWeekChange(weekIdx + 1)}
            >
              Next Week <EastIcon />
            </Button>
          </Box>
        )}
        <div className="bar-chart-wrap">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart
              data={data}
              margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              barSize={30}
            >
              <CartesianGrid
                strokeDasharray="0"
                stroke="#D4D6D8"
                vertical={false}
              />
              <XAxis
                dataKey="name"
                fontSize={12}
                fontFamily="Inter"
                color="#989898"
                padding={{ left: 16, right: 16 }}
              />
              <YAxis fontSize={12} fontFamily="Inter" color="#989898" />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{ fill: "transparent" }}
                shared={false}
              />
              <Legend content={<CustomLegend />} />
              {dataKeys?.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  name={
                    key.charAt(0).toUpperCase() +
                    key.slice(1).replace(/([A-Z])/g, " $1")
                  }
                  fill={colors[index]}
                  radius={[6, 6, 0, 0]}
                  isAnimationActive={false}
                  barSize={barSize || 30}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Box>
    </Box>
  );
};

export default ChartCard;
