import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
import { Link } from "react-router-dom";
import StatisticsTableModal from "./ActivityTableModal";
import CSVExport from "../csvExport/CSVExport";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import DetailsTableModal from "./DetailsTableModal";
import ButtonComponent from "../../library/common/components/Button";
import Dropzone from "react-dropzone";
import "./users.scss";
import { config } from "../../helpers/config";
import CopyToClipboard from "react-copy-to-clipboard";
import ReferralDetail from "./referralDetailModal";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import _ from "lodash";
import DateFnsUtils from "@date-io/date-fns";
import differenceInYears from "date-fns/differenceInYears";
import Select from "react-select";
import { ReactComponent as Unchecked } from "../../images/checkboxUnChecked.svg";
import { ReactComponent as Checked } from "../../images/checkBoxChecked.svg";
import { ReactComponent as Indeterminent } from "../../images/indeterminent-icon.svg";
import { ReactComponent as Football } from "../../images/Sport/football.svg";
import { ReactComponent as Baseball } from "../../images/Sport/baseballLight.svg";
import { ReactComponent as Basketball } from "../../images/Sport/basketballLight.svg";
import { ReactComponent as Boxing } from "../../images/Sport/boxingLight.svg";
import { ReactComponent as Cricket } from "../../images/Sport/cricketLight.svg";
import { ReactComponent as Golf } from "../../images/Sport/golfLight.svg";
import { ReactComponent as IceHockey } from "../../images/Sport/iceHockey-new.svg";
import { ReactComponent as MMA } from "../../images/Sport/mmaIcon-new.svg";
import { ReactComponent as Rugby } from "../../images/Sport/rugby.svg";
import { ReactComponent as Soccer } from "../../images/Sport/soccerIcon-new.svg";
import { ReactComponent as Tennis } from "../../images/Sport/Tennis.svg";
import { ReactComponent as AR } from "../../images/Sport/arIcon-new.svg";
import { ReactComponent as RU } from "../../images/Sport/rugbyUnionIcon-new.svg";
import { ReactComponent as RaceHorses } from "../../images/Sport/horseRacingIcon.svg";
import fantasyAxiosInstance from "../../helpers/Axios/fantasyAxios";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const verifyStatusOptions = [
  { id: 1, value: "true", label: "Verified" },
  { id: 2, value: "false", label: "Not Verified" },
];
const roleOptions = [
  { id: 1, value: "member", label: "Member" },
  { id: 2, value: "admin", label: "Admin" },
  { id: 3, value: "expertTip", label: "Expert Tip" },
];
const statusUpdateOptions = [
  {
    label: "active",
    value: 1,
  },
  {
    label: "deleted",
    value: 2,
  },
  {
    label: "blocked",
    value: 3,
  },
];
class Users extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      users: [],
      isInputModalOpen: false,
      isActivityModalOpen: false,
      isDetailsModalOpen: false,
      isBankDetailsModalOpen: false,
      isBankDetailsLoader: false,
      isImportModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      values: {
        firstName: "",
        lastName: "",
        username: null,
        password: "",
        role: "member",
        otpcode: "",
        isVarify: "true",
        status: "active",
        wpUserName: "",
        wpEmail: "",
        wpPassword: "",
        dob: null,
        country: "",
        stateVal: "",
      },
      errorFirstName: "",
      errorLastName: "",
      errorUsername: "",
      errorValidEmail: "",
      errorPassword: "",
      errorDOB: "",
      errorCountry: "",
      errorState: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      usersCount: 0,
      csvListData: [],
      userDetail: [],
      selectedFile: null,
      isReferralDetailsModalOpen: false,
      userIdReferral: null,
      userDetailReferral: [],

      // state: null,
      stateAll: [],
      country: null,
      countryAll: [],
      // page: 0,
      count: "",
      pageCountry: 0,
      countCountry: "",
      searchCountry: [],
      searchCountryCount: "",
      SearchCountrypage: 0,
      isCountrySearch: "",
      pageState: 0,
      countState: "",
      searchState: [],
      searchStateCount: "",
      SearchStatepage: 0,
      isStateSearch: "",
      randompassword: "",
      showRepeatPassword: true,
      isImportUserDetailsModalOpen: false,
      importDetails: {},
      importDetailsMessage: "",
      isWpPasswordShown: true,
      sports: [],
      isLoader: false,
      bankDetails: {},
      bankDetailsMsg: "",
    };
  }

  componentDidMount() {
    this.fetchAllUsers("", 0);

    if (this.state.isEditMode) {
      this.fetchCurrentUser(this.state.id);
    } else {
      this.generateRandomPassword(8);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllUsers(this.state?.search, this.state.offset);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllUsers("", 0);
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
      });
    }
  }

  generateRandomPassword = (length) => {
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-+=?";
    let passwords = "";

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      passwords += charset[randomIndex];
    }
    return this.setState({
      randompassword: passwords,
    });
    // return this.setState({
    //   values: {
    //     ...this.state?.values,
    //     password: passwords,
    //   },
    // });
  };

  fetchGender = (gender) => {
    if (gender == "m") {
      return "Male";
    } else if (gender == "f") {
      return "Female";
    } else if (gender == "nb") {
      return "Non-binary";
    } else if (gender == "pnts") {
      return "Prefer not to say";
    } else {
      return null;
    }
  };

  fetchSportName = (sportId) => {
    const sportNames = {
      1: "HorseRacingEmail",
      2: "HarnessEmail",
      3: "GreyhoundEmail",
      15: "AmericanFootballEmail",
      9: "AustralianRulesEmail",
      11: "BaseballEmail",
      10: "BasketballEmail",
      6: "BoxingEmail",
      4: "CricketEmail",
      16: "GolfEmail",
      17: "IceHockeyEmail",
      5: "MixedMartialArtsEmail",
      12: "RugbyLeagueEmail",
      13: "RugbyUnionEmail",
      8: "SoccerEmail",
      7: "TennisEmail",
    };
    return sportNames[sportId];
  };

  fetchAllCsvData = async () => {
    let { search } = this.state;
    try {
      const { status, data } = await axiosInstance.get(
        URLS.users + `?search=${search}`
      );
      if (status === 200) {
        const csvData = data?.result?.data?.map((item) => {
          const bookMakerValues = item?.usermeta
            ?.filter((item) => item?.key === "bookMaker")
            ?.map((item) => item?.value);
          const sportOrEventValues = item?.usermeta
            ?.filter((item) => item?.key === "sportOrEvent")
            ?.map((item) => item?.value);
          const offeringsValues = item?.usermeta
            ?.filter((item) => item?.key === "offerings")
            ?.map((item) => item?.value);
          const result = {};
          const sportDataInfo =
            item?.NotificationPreference?.sportsValue?.forEach((sub) => {
              const { SportId, ...rest } = sub;
              const trueKeys = Object.keys(rest).filter(
                (key) => rest[key] === true
              );

              const sportName = this.fetchSportName(SportId);
              if (sportName) {
                result[sportName] = JSON.stringify(trueKeys.toString());
              }
            });

          return {
            Id: item?.id,
            FirstName: item?.firstName,
            LastName: item?.lastName,
            DOB: item?.dob
              ? moment(item?.dob).tz(timezone).format("DD-MM-YYYY")
              : "-",
            Email: item?.username,
            Status: item?.status,
            UserName: item?.nickName,
            Gender: this.fetchGender(item?.gender),
            PlatformType: item?.platFormType,
            Bookmaker: JSON.stringify(
              bookMakerValues
                ?.map((value, index) => {
                  return value !== "Other - Please specify" && value;
                })
                .toString()
            ),
            PhoneNumber: item?.Country?.phoneCode
              ? `+${item?.Country?.phoneCode} `
              : "" + " " + item?.phone,
            SportOrEvent: JSON.stringify(
              sportOrEventValues
                ?.map((value, index) => {
                  return value !== "Other - Please specify" && value;
                })
                .toString()
            ),
            // Offerings: JSON.stringify(
            //   offeringsValues
            //     ?.map((value, index) => {
            //       return value !== "Other - Please specify" && value;
            //     })
            //     .toString()
            // ),
            // References: JSON.stringify(
            //   item?.hearedAbout
            //     ?.map((value, index) => {
            //       if (value?.includes("other")) {
            //         return value?.split(":")[1];
            //       }
            //       return value;
            //     })
            //     .toString()
            // ),
            signupDate: item?.createdAt
              ? moment(item?.createdAt).tz(timezone).format("DD-MM-YYYY")
              : "-",
            // SportOrEvent: JSON.stringify(
            //   item?.usermeta
            //     ?.map((item) => {
            //       return item?.key === "sportOrEvent" &&
            //         item?.value != "Other - Please specify"
            //         ? item?.value
            //         : "";
            //     })
            //     .toString()
            // ),
            // Offerings: JSON.stringify(
            //   item?.usermeta
            //     ?.map((item) => {
            //       return item?.key === "offerings" &&
            //         item?.value != "Other - Please specify"
            //         ? item?.value
            //         : "";
            //     })
            //     .toString()
            // ),
            DailyBestBetEmail: item?.NotificationPreference?.dailyBestBet
              ? "Yes"
              : "No",
            TipOfTheDayEmail: item?.NotificationPreference?.tipOfTheDay
              ? "Yes"
              : "No",
            BlackBookEmail: item?.NotificationPreference?.blackBook
              ? "Yes"
              : "No",
            BAWNewslettersEmail:
              item?.NotificationPreference?.weeklyNewsLetter &&
              item?.NotificationPreference?.weeklyNewsLetter?.length > 0
                ? JSON.stringify(
                    item?.NotificationPreference?.weeklyNewsLetter?.toString()
                  )
                : "",
            SmartBNewsletter: item?.NotificationPreference?.smartBNewsLetter
              ? "Yes"
              : "No",
            SmartBookEmail: item?.NotificationPreference?.smartBook
              ? "Yes"
              : "No",
            TippingCompetitionEmail:
              item?.NotificationPreference?.tippingCompetition &&
              item?.NotificationPreference?.tippingCompetition?.length > 0
                ? JSON.stringify(
                    item?.NotificationPreference?.tippingCompetition?.toString()
                  )
                : "",
            ...result,
          };
        });
        this.setState({
          csvListData: csvData,
        });
      }
    } catch {}
  };

  async fetchAllUsers(search, offset) {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.users + `?search=${search}&limit=20&offset=${offset}&allUser=true`
    );
    if (status === 200) {
      this.fetchAllCsvData();
      this.setState({
        users: data?.result?.data,
        isLoading: false,
        usersCount: data?.result?.count,
      });
    }
  }

  handleValidate = () => {
    let { values } = this.state;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    let flag = true;
    if (values?.firstName?.trim() === "" || values?.firstName === null) {
      flag = false;
      this.setState({
        errorFirstName: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorFirstName: "",
      });
    }
    // if (values?.lastName?.trim() === "" || values?.lastName === null) {
    //   flag = false;
    //   this.setState({
    //     errorLastName: "This field is mandatory",
    //   });
    //   // return flag;
    // } else {
    //   this.setState({
    //     errorLastName: "",
    //   });
    // }
    if (values?.username?.trim() == "" || values?.username === null) {
      flag = false;
      this.setState({
        errorUsername: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorUsername: "",
      });
    }
    if (!emailRegex.test(values?.username)) {
      flag = false;
      this.setState({
        errorValidEmail: "Please enter a valid email",
      });
      // return flag;
    } else {
      this.setState({
        errorValidEmail: "",
      });
    }

    if (
      !this.state?.isEditMode &&
      (values?.password?.trim() === "" || values?.password === null)
    ) {
      flag = false;
      this.setState({
        errorPassword: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorPassword: "",
      });
    }

    // if (values?.dob === "" || values?.dob === null) {
    //   flag = false;
    //   this.setState({
    //     errorDOB: "This field is mandatory",
    //   });
    //   // return flag;
    // } else
    if (differenceInYears(new Date(), new Date(values?.dob)) < 18) {
      flag = false;
      this.setState({
        errorDOB: "You have to be at least 18",
      });
      // return flag;
    } else {
      this.setState({
        errorDOB: "",
      });
    }

    // if (values?.country === "" || values?.country === null) {
    //   flag = false;
    //   this.setState({
    //     errorCountry: "This field is mandatory",
    //   });
    //   // return flag;
    // } else {
    //   this.setState({
    //     errorCountry: "",
    //   });
    // }
    // if (values?.stateVal === "" || values?.stateVal === null) {
    //   flag = false;
    //   this.setState({
    //     errorState: "This field is mandatory",
    //   });
    //   // return flag;
    // } else {
    //   this.setState({
    //     errorState: "",
    //   });
    // }

    return flag;
  };

  handleSave = async () => {
    const { values, isEditMode, sports } = this.state;
    if (this.handleValidate()) {
      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.users}/${this.state?.idToSend}`
        : URLS.createUser;

      const notificationData = sports?.map((item) => {
        const commonProps = {
          tips: item?.notificationData?.[0]?.isChecked || false,
          news: item?.notificationData?.[1]?.isChecked || false,
          fixtures: item?.notificationData?.[2]?.isChecked || false,
        };
        if (item?.id === 1 || item?.id === 2 || item?.id === 3) {
          return {
            SportId: item?.id,
            tips: item?.notificationData?.[0]?.isChecked || false,
            news: item?.notificationData?.[1]?.isChecked || false,
            dailyBestBet: item?.notificationData?.[2]?.isChecked || false,
            fixtures: item?.notificationData?.[3]?.isChecked || false,
            weeklyNewsLetter: item?.notificationData?.[4]?.isChecked || false,
            smartBNewsLetter: item?.notificationData?.[5]?.isChecked || false,
          };
        } else {
          return {
            SportId: item?.id,
            ...commonProps,
          };
        }
      });
      let sportId1 =
        (await notificationData?.length) > 0 &&
        notificationData?.find((item) => item.SportId === 1);
      if (sportId1) {
        var allSportsNotificationData = [
          ...notificationData,
          { ...sportId1, SportId: 2 },
          { ...sportId1, SportId: 3 },
        ];
      }

      // let address = this.state.address;
      let payload = {
        firstName: values?.firstName,
        isVarify: values?.isVarify,
        lastName: values?.lastName ? values?.lastName : "",
        role: values?.role,
        status: values?.status,
        username: values?.username,
        dob: values?.dob ? values?.dob : null,
        NotificationPreference: allSportsNotificationData,
        address: {
          // addressLine1: address?.address,
          // addressLine2: address?.addressLine2,
          state: values?.stateVal ? values?.stateVal : null,
          country: values?.country ? values?.country : null,
          // postCode: address?.postCode,
        },
      };
      if (!isEditMode) {
        payload = {
          ...payload,
          platformType: "admin",
        };
      }
      if (values?.role == "admin" || values?.role == "expertTip") {
        payload = {
          ...payload,
          wpUserName: values?.wpUserName ? values?.wpUserName : null,

          wpEmail: values?.wpEmail ? values?.wpEmail : null,
        };
        if (values?.wpPassword && values?.wpPassword.trim().length > 0) {
          payload.wpPassword = values?.wpPassword;
        }
      }
      if (!isEditMode) {
        payload = {
          ...payload,
          password: values?.password,
        };
      } else {
        payload = {
          ...payload,
          ...(values?.password && { password: values?.password }),
        };
      }
      this.setState({ isLoading: true });
      try {
        const { status, data } = await axiosInstance[method](url, payload);
        if (status === 200) {
          this.toggleInputModal();
          this.fetchAllUsers("", 0);
          this.setState({
            isLoading: false,
            search: "",
            offset: 0,
            currentPage: 1,
          });
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({
            isLoading: false,
            search: "",
            offset: 0,
            currentPage: 1,
          });
          this.toggleInputModal();
        }
      } catch (err) {
        this.toggleInputModal();
        this.setActionMessage(true, "Error", err?.response?.data?.message);

        this.setState({
          isLoading: false,
          search: "",
          offset: 0,
          currentPage: 1,
        });
      }
    }
  };

  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        countryCount: Math.ceil(count),
      });
    }
  };

  fetchAllState = async (id, pages, userInfo) => {
    const { status, data } = await axiosInstance.get(
      URLS.state + `/country/${id}?limit=20&offset=${pages}`
      // `${URLS.distance}/country/${id}?size=20&page=${pageState}`
      // `${URLS.country}?limit=20&offset=${pageState}`
    );

    if (status === 200) {
      let Parray = [...this.state.stateAll];

      let count = data?.result?.count / 20;

      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.state,
          value: item?.id,
        });
      });
      let selectedState = {
        label: userInfo?.address?.State?.state,
        value: userInfo?.address?.State?.id,
      };
      let filterData = [...this.state.stateAll, ...newdata, selectedState];

      let sortData = filterData?.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });
      this.setState({
        stateAll: _.uniqBy(sortData, function (e) {
          return e.value;
        }),
        countState: Math.ceil(count),
      });

      // this.setState({
      //   stateAll: _.unionBy(this.state.stateAll, newdata),
      //   countState: Math.ceil(count),
      // });
    }
  };

  // setMedia = async (files) => {
  //   const formData = new FormData();
  //   formData.append("image", files ? files : null);
  //   if (files !== undefined) {
  //     const { status, data } = await axiosInstance.post(URLS.media, formData, {
  //       header: { "Content-Type": "multipart/form-data" },
  //     });
  //     if (status === 200) {
  //       return data;
  //     } else {
  //       return false;
  //     }
  //   }
  //   return false;
  // };
  async uploadUserFile() {
    this.setState({ isLoading: true });
    let { selectedFile } = this.state;
    const formData = new FormData();
    formData.append("userInfo", selectedFile ? selectedFile : null);
    // const payload = {
    //   userInfo: { selectedFile },
    // };
    const { status, data } = await axiosInstance.post(
      URLS.users + `/addUsers`,
      formData,
      {
        header: { "Content-Type": "multipart/form-data" },
      }
    );
    if (status === 200) {
      this.setState({
        isLoading: false,
        selectedFile: null,
        isImportUserDetailsModalOpen: true,
        importDetails: data?.result,
        importDetailsMessage: data?.message,
        // isImportModalOpen: false,
      });
      // this.setActionMessage(true, "Success", "File Uploaded Successfully!");
    }
  }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      values: {
        firstName: "",
        lastName: "",
        username: null,
        password: "",
        role: "member",
        otpcode: "",
        isVarify: "true",
        status: "active",
        wpUserName: "",
        dob: null,
        country: "",
        stateVal: "",
      },
      errorFirstName: "",
      errorLastName: "",
      errorUsername: "",
      errorValidEmail: "",
      errorPassword: "",
      errorDOB: "",
      errorCountry: "",
      errorState: "",
      stateAll: [],
    });
  };

  openActivityModal = (item) => {
    this.setState({ isActivityModalOpen: true, userId: item?.id });
  };
  openDetailsModal = (item) => {
    this.setState({
      isDetailsModalOpen: true,
      userId: item?.id,
      userDetail: item,
    });
  };

  openBankDetailsModal = async (item) => {
    this.setState({
      isBankDetailsModalOpen: true,
    });
    this.setState({ isBankDetailsLoader: true });
    try {
      const { status, data } = await fantasyAxiosInstance.get(
        `/withdraw/get-bank/${item?.id}`
      );
      if (status === 200) {
        this.setState({
          isBankDetailsLoader: false,
          bankDetails: data?.result,
          bankDetailsMessage: data?.message,
        });
      } else {
        this.setState({
          isBankDetailsLoader: false,
        });
      }
    } catch {
      this.setState({
        isBankDetailsLoader: false,
      });
    }
  };

  toggleActivityModal = () => {
    this.setState({ isActivityModalOpen: !this.state.isActivityModalOpen });
  };
  toggleDetailsModal = () => {
    this.setState({ isDetailsModalOpen: !this.state.isDetailsModalOpen });
  };

  toggleBankDetailsModal = () => {
    this.setState({
      isBankDetailsModalOpen: !this.state.isBankDetailsModalOpen,
      bankDetails: {},
      bankDetailsMessage: "",
    });
  };

  openReferralDetailsModal = (item) => {
    this.setState({
      isReferralDetailsModalOpen: true,
      userIdReferral: item?.id,
      userDetailReferral: item,
    });
  };

  toggleReferralDetailsModal = () => {
    this.setState({
      isReferralDetailsModalOpen: !this.state.isReferralDetailsModalOpen,
    });
  };

  fetchCurrentUser = async (id) => {
    this.setState({ isLoader: true });
    const { status, data } = await axiosInstance.get(URLS.users + `/${id}`);
    if (status === 200) {
      this.setState({ values: data, isLoader: false });
      if (data?.address?.country) {
        this.fetchAllState(data?.address?.country, 0, data);
      }

      let newdata = {
        label: data?.address?.Country?.country,
        value: data?.address?.Country?.id,
      };
      this.setState({
        countryAll: [...this?.state?.countryAll, newdata],
      });
      this.setState(() => {
        return {
          values: {
            ...this.state.values,
            firstName: data?.firstName,
            lastName: data?.lastName,
            username: data?.username,
            role: data?.role,
            status: data?.status,
            wpUserName: data?.wpUserName,
            wpEmail: data?.wpEmail,
            dob: data?.dob,
            isVarify: data?.isVarify == true ? "true" : "false",
            country: data?.address?.country,
            stateVal: data?.address?.state,
            wpPassword: "",
          },
        };
      });
    }
  };

  inputModal = (id, type, userData) => () => {
    this.fetchAllCountry(0);
    this.fetchSportData(userData);
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
      this.fetchCurrentUser(id);
    } else {
      this.setState({
        isEditMode: false,
        values: {
          firstName: "",
          lastName: "",
          username: null,
          password: this.state?.randompassword,
          role: "member",
          otpcode: "",
          isVarify: "true",
          status: "active",
          wpUserName: "",
          dob: null,
          country: "",
          stateVal: "",
        },
        errorFirstName: "",
        errorLastName: "",
        errorUsername: "",
        errorValidEmail: "",
        errorPassword: "",
        errorDOB: "",
        errorCountry: "",
        errorState: "",
      });
      this.generateRandomPassword(8);
    }
  };
  importFileModal = () => () => {
    this.setState({
      isImportModalOpen: true,
      selectedFile: null,
    });
  };

  toggleImportModal = () => {
    this.setState({
      isImportModalOpen: !this.state.isImportModalOpen,
    });
  };

  toggleImportUserDetailsModal = () => {
    this.setState({
      isImportUserDetailsModalOpen: !this.state.isImportUserDetailsModalOpen,
      isImportModalOpen: false,
    });
    // this.setActionMessage(true, "Success", this.state?.importDetailsMessage);
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllUsers("", 0);
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.users}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState(
          {
            itemToDelete: null,
            isModalOpen: false,
            search: "",
            offset: 0,
            currentPage: 1,
          },
          () => {
            this.fetchAllUsers("", 0);
          }
        );
        this.setActionMessage(true, "Success", "User Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, users } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < users.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
    this.fetchAllUsers("", 0);
  };

  handleKeyDown = (event) => {
    if (event.key === "Enter") {
      this.fetchAllUsers(this.state?.search, 0);
      this.setState({ currentPage: 1 });
    }
  };

  // handleFileChange = (event) => {
  //   const file = event.target.files[0];
  //   if (file) {
  //     this.setState({
  //       selectedFile: file,
  //     });
  //   }
  // };

  handleUpload = () => {
    // if (this.state.selectedFile) {
    //   const reader = new FileReader();
    //   reader.onload = (event) => {
    //     const csvData = event.target.result;
    //     // Do something with the CSV data, e.g., parse it or send it to a server
    //   };
    //   reader.readAsText(this.state.selectedFile);
    // }
    this.uploadUserFile();
  };

  handleFileDrop = (acceptedFiles) => {
    // Assuming you want to handle only the first file if multiple files are dropped
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];
      this.setState({ selectedFile });
    }
  };

  handleCopy = () => {
    this.setActionMessage(
      true,
      "Success",
      "Referral link copied successfully!"
    );
  };

  handleOnScrollBottomCountry = () => {
    let {
      // countCountry,
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      count,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (count !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };
  handleOnScrollBottomState = () => {
    let {
      countState,
      pageState,
      isStateSearch,
      searchStateCount,
      SearchStatepage,
      values,
    } = this.state;

    if (
      isStateSearch !== "" &&
      searchStateCount !== Math.ceil(SearchStatepage / 20)
    ) {
      this.handleStateInputChange(SearchStatepage + 20, isStateSearch);
      this.setState({
        SearchStatepage: SearchStatepage + 20,
      });
    } else {
      if (values?.country !== null) {
        if (countState !== Math.ceil(pageState / 20)) {
          this.fetchAllState(
            values?.country ? values?.country : null,
            pageState + 20
          );
          this.setState({
            pageState: pageState + 20,
          });
        }
      }
    }
  };
  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
    // } else {
    //   this.setState({
    //     isCountrySearch: "",
    //     SearchCountrypage: 1,
    //     searchCountry: [],
    //   });
    // }
  };
  handleStateInputChange = (page, value) => {
    const { rowToPass } = this.props;
    // if (value.length > 2) {
    axiosInstance
      .get(
        `${URLS.state}/country/${this.state?.values?.country}?limit=20&offset=${page}&search=${value}`
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;

          let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.state,
              value: item?.id,
            });
          });
          const finalStateData = newdata.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          });
          let filterData = _.unionBy(this.state.searchState, finalStateData);

          this.setState({
            searchState: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchStateCount: Math.ceil(count),
            isStateSearch: value,
          });
        }
      });
    // } else {
    //   this.setState({
    //     isStateSearch: "",
    //     SearchStatepage: 1,
    //     searchState: [],
    //   });
    // }
  };

  handleSportChange = (sportId) => {
    const { sports } = this.state;
    const updatedSports = sports.map((sport) => {
      if (Array.isArray(sport?.id) && sport?.id.includes(sportId)) {
        const updatedSubscriptions = sport?.notificationData?.map((sub) => ({
          ...sub,
          isChecked: !sub?.isChecked,
        }));
        return {
          ...sport,
          notificationData: updatedSubscriptions,
        };
      } else if (sport?.id === sportId) {
        return {
          ...sport,
          isChecked: !sport?.isChecked,
          isIndeterminate: false,
          notificationData: sport?.notificationData?.map((sub) => ({
            ...sub,
            isChecked: !sport?.isChecked,
          })),
        };
      }
      return sport;
    });

    this.setState({ sports: updatedSports });
  };

  fetchSportIcon = (sportId) => {
    switch (sportId) {
      case 1:
        return <RaceHorses />;
      case 15:
        return <Football />;
      case 9:
        return <AR />;
      case 11:
        return <Baseball />;
      case 10:
        return <Basketball />;
      case 6:
        return <Boxing />;
      case 4:
        return <Cricket />;
      case 16:
        return <Golf />;
      case 17:
        return <IceHockey />;
      case 5:
        return <MMA />;
      case 12:
        return <Rugby />;
      case 13:
        return <RU />;
      case 8:
        return <Soccer />;
      case 7:
        return <Tennis />;
      default:
        return null; // Return null for unrecognized sportIds
    }
  };

  handleSubscriptionChange = (sportId, subId) => {
    const { sports } = this.state;
    const updatedSports = sports.map((sport) => {
      if (sport?.id === sportId) {
        const updatedSubscriptions = sport?.notificationData?.map((sub) => {
          if (sub?.id === subId) {
            return {
              ...sub,
              isChecked: !sub?.isChecked,
            };
          }
          return sub;
        });

        const allChecked = updatedSubscriptions?.every((sub) => sub?.isChecked);
        const someChecked = updatedSubscriptions?.some((sub) => sub?.isChecked);
        const isChecked = allChecked || (!allChecked && someChecked);

        return {
          ...sport,
          notificationData: updatedSubscriptions,
          isChecked,
          isIndeterminate: !allChecked && someChecked,
        };
      }
      return sport;
    });
    this.setState({ sports: updatedSports });
  };

  fetchSportData = async (newData) => {
    try {
      const { data } = await axiosInstance.get(
        `/sports/sport/?sportTypeId=${2}`
      );

      const SportsData = data?.result?.map((item) => ({
        ...item,
        notificationData: [
          { id: 1, subName: "Tips" },
          { id: 2, subName: "News" },
          { id: 3, subName: "Fixtures & Results" },
        ],
      }));

      const sortedData = SportsData?.sort((a, b) =>
        a?.sportName.localeCompare(b?.sportName)
      );
      const mergeData = [
        ...sortedData,
        {
          id: 1,
          sportName: "Racing",
          SportId: 1,
          isChecked: false,
          isIndeterminate: false,
          notificationData: [
            { id: 1, subName: "Tips of the Day" },
            { id: 2, subName: "News" },
            { id: 4, subName: "Daily Best Bet" },
            { id: 3, subName: "Fixtures & Results" },
            { id: 5, subName: "Weekly Newsletter" },
            { id: 6, subName: "SmartB Newsletter" },
          ],
        },
      ];
      const SelectedData = mergeData?.map((item) => {
        const filteredSports =
          newData?.NotificationPreference?.sportsValue?.length > 0 &&
          newData?.NotificationPreference?.sportsValue?.filter(
            (ele) => ele?.SportId === item?.id
          );
        const allChecked =
          filteredSports?.[0]?.SportId == 1
            ? filteredSports?.[0]?.tips &&
              filteredSports?.[0]?.news &&
              filteredSports?.[0]?.dailyBestBet &&
              filteredSports?.[0]?.fixtures &&
              filteredSports?.[0]?.weeklyNewsLetter &&
              filteredSports?.[0]?.smartBNewsLetter
            : filteredSports?.[0]?.tips &&
              filteredSports?.[0]?.news &&
              filteredSports?.[0]?.fixtures;
        const someChecked =
          filteredSports?.[0]?.SportId == 1
            ? filteredSports?.[0]?.tips ||
              filteredSports?.[0]?.news ||
              filteredSports?.[0]?.dailyBestBet ||
              filteredSports?.[0]?.fixtures ||
              filteredSports?.[0]?.weeklyNewsLetter ||
              filteredSports?.[0]?.smartBNewsLetter
            : filteredSports?.[0]?.tips ||
              filteredSports?.[0]?.news ||
              filteredSports?.[0]?.fixtures;

        const isChecked = allChecked || (!allChecked && someChecked);

        return {
          ...item,
          isChecked,
          isIndeterminate: !allChecked && someChecked,
          notificationData: item?.notificationData?.map((ele, index) => ({
            ...ele,
            isChecked:
              item?.id == 1
                ? index === 0
                  ? filteredSports?.[0]?.tips
                  : index === 1
                  ? filteredSports?.[0]?.news
                  : index === 2
                  ? filteredSports?.[0]?.dailyBestBet
                  : index === 3
                  ? filteredSports?.[0]?.fixtures
                  : index === 4
                  ? filteredSports?.[0]?.weeklyNewsLetter
                  : index === 5
                  ? filteredSports?.[0]?.smartBNewsLetter
                  : false
                : index === 0
                ? filteredSports?.[0]?.tips
                : index === 1
                ? filteredSports?.[0]?.news
                : index === 2
                ? filteredSports?.[0]?.fixtures
                : false,
          })),
        };
      });
      this.setState({
        sports: newData ? SelectedData : mergeData,
      });
    } catch (err) {
      // Handle error
    }
  };

  render() {
    var {
      users,
      isModalOpen,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      isInputModalOpen,
      isEditMode,
      isActivityModalOpen,
      isDetailsModalOpen,
      isBankDetailsModalOpen,
      isImportModalOpen,
      userId,
      csvListData,
      search,
      userDetail,
      selectedFile,
      isReferralDetailsModalOpen,
      userIdReferral,
      userDetailReferral,
      usersCount,
      values,
      countryAll,
      stateAll,
      cityAll,
      country,
      city,
      checkedRace,
      isCountrySearch,
      searchCountry,
      searchState,
      isStateSearch,
      showRepeatPassword,
      errorFirstName,
      errorLastName,
      errorUsername,
      errorValidEmail,
      errorPassword,
      errorDOB,
      errorCountry,
      errorState,
      isImportUserDetailsModalOpen,
      importDetails,
      isWpPasswordShown,
      sports,
      isLoader,
      isBankDetailsLoader,
      bankDetails,
      bankDetailsMessage,
    } = this.state;
    const pageNumbers = [];
    if (usersCount > 0) {
      for (let i = 1; i <= Math.ceil(usersCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            <Box style={{ position: "fixed", width: "100%", zIndex: "10" }}>
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
            </Box>
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  User Management
                </Link>
                <Typography className="active_p">User</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="space-around">
              <Grid item xs={4}>
                {/* <h3 className="text-left">Users</h3> */}
                <Typography variant="h1" align="left">
                  Users
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
                // className="admin-filter-wrap"
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({
                      ...this.state.search,
                      search: e.target.value,
                    });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                    marginLeft: "10px",
                  }}
                  onClick={() => {
                    this.fetchAllUsers(search, 0);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginLeft: "10px",
                  }}
                  onClick={this.inputModal(null, "create", "")}
                >
                  Add User
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginLeft: "10px",
                  }}
                  onClick={this.importFileModal()}
                >
                  Import User
                </Button>
                <Box style={{ marginLeft: "10px" }}>
                  <CSVExport data={csvListData} filename="user_info_data.csv" />
                </Box>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && users?.length === 0 && <p>No Data Available</p>}
            {!isLoading && users?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{
                      minWidth: "max-content",
                    }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Full Name</TableCell>
                        <TableCell>User Name</TableCell>
                        <TableCell>Sign Up Date</TableCell>
                        {/* <TableCell>Subscription</TableCell> */}
                        {/* <TableCell>Passowrd</TableCell> */}
                        <TableCell>Role</TableCell>
                        {/* <TableCell>Address</TableCell> */}
                        <TableCell>Verify Status</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Referral Link</TableCell>
                        <TableCell>Fantasy Link</TableCell>
                        <TableCell>SignUp Type</TableCell>
                        <TableCell>Referral Detail</TableCell>
                        {/* <TableCell>Otp Code</TableCell> */}
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {users?.map((user, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{user?.id}</TableCell>
                          <TableCell>
                            {user?.firstName} {user?.lastName}
                          </TableCell>
                          <TableCell>{user?.username}</TableCell>
                          <TableCell>
                            {moment(user?.createdAt)
                              .tz(timezone)
                              .format("DD-MM-YYYY")}
                          </TableCell>
                          {/* <TableCell>
                          {user?.SubscriptionPurchased !== null
                            ? "active"
                            : "inactive"}
                        </TableCell> */}
                          {/* <TableCell>{user.password}</TableCell> */}
                          <TableCell>{user?.role}</TableCell>
                          {/*} <TableCell>
                            {user?.address &&
                              typeof user?.address === "object" && (
                                <>
                                  {user?.address?.addressLine1 &&
                                    user?.address?.addressLine1 + " "}
                                  {user?.address?.addressLine2 &&
                                    user?.address?.addressLine2 + " "}
                                  {user?.address?.state &&
                                    user?.address?.state + ", "}
                                  {user?.address?.country &&
                                    user?.address?.country}
                                </>
                              )}
                                  </TableCell> {*/}
                          <TableCell>
                            {user?.isVarify ? "Verified" : "not Verified"}
                          </TableCell>
                          <TableCell>{user?.status}</TableCell>
                          <TableCell>
                            <CopyToClipboard
                              text={
                                config?.baseUrl?.replace("/api", "") +
                                `sign-up?referral_type=signup&referral=${user?.referralCode}`
                              }
                              onCopy={() => this.handleCopy()}
                            >
                              <Box
                                className="cursor-pointer"
                                style={{
                                  textAlign: "center",
                                  textDecoration: "underline",
                                  color: "#4455c7",
                                }}
                              >
                                Link
                              </Box>
                            </CopyToClipboard>
                          </TableCell>
                          <TableCell>
                            <CopyToClipboard
                              text={
                                config?.baseUrl?.replace("/api", "") +
                                `fantasy?referral_type=fantasy&referral=${user?.referralCode}`
                              }
                              onCopy={() => this.handleCopy()}
                            >
                              <Box
                                className="cursor-pointer"
                                style={{
                                  textAlign: "center",
                                  textDecoration: "underline",
                                  color: "#4455c7",
                                }}
                              >
                                Fantasy Link
                              </Box>
                            </CopyToClipboard>
                          </TableCell>
                          <TableCell>
                            {user?.platFormType == "admin"
                              ? "Admin"
                              : user?.platFormType == "drt"
                              ? "DRT"
                              : user?.platFormType == "baw"
                              ? "BAW"
                              : user?.platFormType == "web"
                              ? "Web"
                              : user?.platFormType == "ios"
                              ? "iOS"
                              : user?.platFormType == "android"
                              ? "Android"
                              : user?.platFormType}
                          </TableCell>
                          {/* <TableCell>{user.otpCode}</TableCell> */}
                          <TableCell>
                            <Button
                              onClick={() =>
                                this.openReferralDetailsModal(user)
                              }
                              className="table-btn info-btn"
                            >
                              Referral Detail
                            </Button>
                          </TableCell>
                          <TableCell>
                            {/* <EditIcon
                            onClick={this.inputModal(user?.id, "edit")}
                            color="primary"
                            className="mr10 cursor iconBtn admin-btn-green"
                          />
                          <DeleteOutlineIcon
                            onClick={this.setItemToDelete(user?.id)}
                            color="secondary"
                            className="cursor iconBtn admin-btn-orange"
                          /> */}
                            <Button
                              onClick={() => this.openBankDetailsModal(user)}
                              className="table-btn info-btn"
                            >
                              Bank Details
                            </Button>
                            <Button
                              onClick={() => this.openDetailsModal(user)}
                              className="table-btn info-btn"
                            >
                              Details
                            </Button>
                            <Button
                              onClick={() => this.openActivityModal(user)}
                              className="table-btn info-btn"
                            >
                              User Activity
                            </Button>

                            <Button
                              onClick={this.inputModal(user?.id, "edit", user)}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(user?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                usersCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="news-modal user-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New User" : "Edit User"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                {/* <CreateUsers
                inputModal={this.toggleInputModal}
                id={this.state.idToSend}
                isEditMode={isEditMode}
                fetchAllUsers={this.afterChangeRefresh}
                setActionMessageUser={this.setActionMessage}
              /> */}

                {isLoader ? (
                  <Box className="flex justify-center">
                    <Loader />
                  </Box>
                ) : (
                  <Grid container className="page-content adminLogin text-left">
                    <Grid item xs={12} className="pageWrapper api-provider">
                      <Grid container>
                        <Grid
                          item
                          xs={4}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            First Name<span className="color-red">*</span>{" "}
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="First Name"
                            value={values?.firstName}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  firstName: e.target.value,
                                },
                              })
                            }
                          />
                          {!values?.firstName && errorFirstName ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorFirstName}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            Last Name
                            {/* <span className="color-red">*</span> */}
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="Last Name"
                            value={values?.lastName}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  lastName: e.target.value,
                                },
                              })
                            }
                          />
                          {/* {!values?.lastName && errorLastName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorLastName}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            Email/Username<span className="color-red">*</span>
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="Email/Username"
                            value={values?.username}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  username: e.target.value,
                                },
                              })
                            }
                          />
                          {!values?.username && errorUsername ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorUsername}
                            </p>
                          ) : (
                            ""
                          )}
                          {values?.username && errorValidEmail ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorValidEmail}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            Password<span className="color-red">*</span>
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            type={showRepeatPassword ? "text" : "password"}
                            placeholder="Password"
                            value={values?.password}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  password: e.target.value,
                                },
                              })
                            }
                            InputProps={{
                              endAdornment: !showRepeatPassword ? (
                                <>
                                  <Typography
                                    className="c-pointer"
                                    onClick={() =>
                                      this.setState({
                                        showRepeatPassword: !showRepeatPassword,
                                      })
                                    }
                                  >
                                    Show
                                  </Typography>
                                </>
                              ) : (
                                <>
                                  <Typography
                                    className="c-pointer"
                                    onClick={() =>
                                      this.setState({
                                        showRepeatPassword: !showRepeatPassword,
                                      })
                                    }
                                  >
                                    Hide
                                  </Typography>
                                </>
                              ),
                            }}
                          />
                          {!values?.password && errorPassword ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorPassword}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        <Grid item xs={4} className="teamsport-dob-wrap">
                          <label className="modal-label">
                            {" "}
                            DOB
                            {/* <span className="color-red">*</span> */}
                          </label>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DesktopDatePicker
                              // disableToolbar
                              variant="inline"
                              format="dd/MM/yyyy"
                              placeholder="DD/MM/YYYY"
                              margin="normal"
                              id="date-picker-inline"
                              inputVariant="outlined"
                              value={
                                values?.dob
                                  ? typeof values?.dob === "string"
                                    ? parseISO(
                                        moment(values?.dob)
                                          ?.tz(timezone)
                                          ?.format("YYYY-MM-DD")
                                      )
                                    : values?.dob
                                  : null
                              }
                              onChange={(e) =>
                                this.setState({
                                  values: {
                                    ...values,
                                    dob: e,
                                  },
                                })
                              }
                              disableFuture
                              KeyboardButtonProps={{
                                "aria-label": "change date",
                              }}
                              autoOk
                              className="details-runner-picker dob-picker"
                            />
                            {errorDOB ? (
                              <p
                                className="errorText"
                                style={{ margin: "0px" }}
                              >
                                {errorDOB}
                              </p>
                            ) : (
                              ""
                            )}
                          </LocalizationProvider>
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          className="teamsport-textfield national-select teamsport-text"
                          // style={{
                          //   // marginTop: "15px",
                          //   display: "flex",
                          //   flexDirection: "column",
                          // }}
                        >
                          <label className="modal-label">
                            {" "}
                            Country
                            {/* <span className="color-red">*</span>{" "} */}
                          </label>
                          <Select
                            className="React cricket-select external-select"
                            classNamePrefix="select"
                            menuPosition="fixed"
                            placeholder="Country"
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomCountry(e)
                            }
                            // isSearchable={false}
                            onInputChange={(e) =>
                              this.handleCountryInputChange(0, e)
                            }
                            value={
                              isCountrySearch
                                ? searchCountry?.find((item) => {
                                    return item?.value === values?.country;
                                  })
                                : countryAll?.find((item) => {
                                    return item?.value === values?.country;
                                  })
                            }
                            onChange={(e) => {
                              this.setState({
                                values: {
                                  ...values,
                                  country: e?.value,
                                  stateVal: "",
                                },
                                stateAll: [],
                                // state: null,
                              });
                              if (e?.value) {
                                this.fetchAllState(e?.value, 0);
                              }
                            }}
                            options={
                              isCountrySearch ? searchCountry : countryAll
                            }
                          />
                          {/* {errorCountry ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px" }}
                        >
                          {errorCountry}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          className="teamsport-textfield national-select teamsport-text"
                          // style={{
                          //   marginTop: "15px",
                          //   display: "flex",
                          //   flexDirection: "column",
                          // }}
                        >
                          <label className="modal-label">
                            State
                            {/* <span className="color-red">*</span> */}
                          </label>

                          <Select
                            className="React cricket-select external-select"
                            // className="select-box-manual select-box-arrow React"
                            classNamePrefix="select"
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomState(e)
                            }
                            onInputChange={(e) =>
                              this.handleStateInputChange(0, e)
                            }
                            placeholder={
                              country
                                ? "No State Selected"
                                : "Select Country First"
                            }
                            value={
                              isStateSearch
                                ? searchState?.find((item) => {
                                    return item?.value == values?.stateVal;
                                  })
                                : values?.stateVal !== "" &&
                                  stateAll?.find((item) => {
                                    return item?.value == values?.stateVal;
                                  })
                            }
                            onChange={(e) => {
                              this.setState({
                                values: {
                                  ...values,
                                  stateVal: e?.value,
                                },
                              });
                            }}
                            options={isStateSearch ? searchState : stateAll}
                          />
                          {/* {errorState ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px" }}
                        >
                          {errorState}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          className="teamsport-textfield national-select teamsport-text"
                          // style={{
                          //   marginTop: "15px",
                          //   display: "flex",
                          //   flexDirection: "column",
                          // }}
                        >
                          <label className="modal-label">Role</label>
                          <Select
                            className="React cricket-select external-select"
                            classNamePrefix="select"
                            placeholder="Select Status"
                            value={roleOptions?.find((item) => {
                              return item?.value == values?.role;
                            })}
                            //   isLoading={isLoading}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  role: e?.value,
                                },
                              })
                            }
                            options={roleOptions}
                          />
                          {/* {errorCountry ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px" }}
                        >
                          {errorCountry}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          className="teamsport-textfield national-select teamsport-text"
                          // style={{
                          //   marginTop: "15px",
                          //   display: "flex",
                          //   flexDirection: "column",
                          // }}
                        >
                          <label className="modal-label">Verify Status</label>
                          <Select
                            className="React cricket-select  external-select"
                            classNamePrefix="select"
                            placeholder="Select Status"
                            value={verifyStatusOptions?.find((item) => {
                              return item?.value == values?.isVarify;
                            })}
                            //   isLoading={isLoading}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  isVarify: e?.value,
                                },
                              })
                            }
                            options={verifyStatusOptions}
                            isDisabled={!isEditMode}
                          />
                          {/* {errorCountry ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px" }}
                        >
                          {errorCountry}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          className="teamsport-textfield national-select teamsport-text"
                          // style={{
                          //   marginTop: "15px",
                          //   display: "flex",
                          //   flexDirection: "column",
                          // }}
                        >
                          <label className="modal-label">Status Update</label>
                          <Select
                            className="React cricket-select external-select"
                            classNamePrefix="select"
                            placeholder="Select Status"
                            value={statusUpdateOptions?.find((item) => {
                              return item?.label == values?.status;
                            })}
                            //   isLoading={isLoading}
                            onChange={(e) =>
                              this.setState({
                                values: {
                                  ...values,
                                  status: e?.label,
                                },
                              })
                            }
                            options={statusUpdateOptions}
                          />
                          {/* {errorCountry ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px" }}
                        >
                          {errorCountry}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                        {(values?.role == "admin" ||
                          values?.role == "expertTip") && (
                          <>
                            <Grid
                              item
                              xs={4}
                              // style={{
                              //   marginTop: "15px",
                              //   display: "flex",
                              //   flexDirection: "column",
                              // }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                {" "}
                                Wordpress User
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Wordpress User"
                                value={values?.wpUserName}
                                onChange={(e) =>
                                  this.setState({
                                    values: {
                                      ...values,
                                      wpUserName: e.target.value,
                                    },
                                  })
                                }
                              />
                              {/* {errorTitle ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorTitle}
                        </p>
                      ) : (
                        ""
                      )} */}
                            </Grid>
                            <Grid
                              item
                              xs={4}
                              // style={{
                              //   marginTop: "15px",
                              //   display: "flex",
                              //   flexDirection: "column",
                              // }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                {" "}
                                Wordpress Email
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Wordpress Email"
                                value={values?.wpEmail}
                                onChange={(e) =>
                                  this.setState({
                                    values: {
                                      ...values,
                                      wpEmail: e.target.value,
                                    },
                                  })
                                }
                              />
                              {/* {errorTitle ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorTitle}
                        </p>
                      ) : (
                        ""
                      )} */}
                            </Grid>
                            <Grid
                              item
                              xs={4}
                              // style={{ display: "flex", flexDirection: "column" }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                {" "}
                                Wordpress Password
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                type={isWpPasswordShown ? "text" : "password"}
                                placeholder="Wordpress Password"
                                value={values?.wpPassword}
                                onChange={(e) =>
                                  this.setState({
                                    values: {
                                      ...values,
                                      wpPassword: e.target.value,
                                    },
                                  })
                                }
                                InputProps={{
                                  endAdornment: !isWpPasswordShown ? (
                                    <>
                                      <Typography
                                        className="c-pointer"
                                        onClick={() =>
                                          this.setState({
                                            isWpPasswordShown:
                                              !isWpPasswordShown,
                                          })
                                        }
                                      >
                                        Show
                                      </Typography>
                                    </>
                                  ) : (
                                    <>
                                      <Typography
                                        className="c-pointer"
                                        onClick={() =>
                                          this.setState({
                                            isWpPasswordShown:
                                              !isWpPasswordShown,
                                          })
                                        }
                                      >
                                        Hide
                                      </Typography>
                                    </>
                                  ),
                                }}
                              />
                            </Grid>
                          </>
                        )}
                      </Grid>

                      <Grid container>
                        <Box className="notification-wrap">
                          {sports?.map((sport) => {
                            return (
                              <Box key={sport?.id} className="parent-checkbox">
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      icon={
                                        <Unchecked className="radio-icon" />
                                      }
                                      checkedIcon={
                                        <Checked className="radio-icon" />
                                      }
                                      indeterminateIcon={<Indeterminent />}
                                      checked={
                                        sport?.isChecked == true &&
                                        sport?.isIndeterminate == false
                                      }
                                      indeterminate={sport?.isIndeterminate}
                                      onChange={() =>
                                        this.handleSportChange(sport?.id)
                                      }
                                    />
                                  }
                                  label={
                                    <Box className="sport-icon-wrap">
                                      <span className="sports-name">
                                        {sport?.sportName}
                                      </span>
                                      <span className="sport-icon">
                                        {this.fetchSportIcon(sport?.id)}
                                      </span>
                                    </Box>
                                  }
                                />
                                <Box className="child-wrap">
                                  {sport?.notificationData?.map((sub) => (
                                    <span
                                      key={sub?.id}
                                      className={`child-checkbox p-25 ${
                                        sub?.isChecked === true
                                          ? "active-label"
                                          : ""
                                      }`}
                                      onClick={() =>
                                        this.handleSubscriptionChange(
                                          sport?.id,
                                          sub?.id
                                        )
                                      }
                                    >
                                      {sub?.subName}
                                    </span>
                                  ))}
                                </Box>
                              </Box>
                            );
                          })}
                        </Box>
                      </Grid>

                      <Grid container>
                        <Grid item xs={2}>
                          <div style={{ marginTop: "20px", display: "flex" }}>
                            {!isEditMode ? (
                              <ButtonComponent
                                className="mt-3 admin-btn-purple"
                                onClick={() => this.handleSave()}
                                color="primary"
                                value={!isLoading ? "Save" : "Loading..."}
                                disabled={isLoading}
                                style={{ minWidth: "auto" }}
                              />
                            ) : (
                              <ButtonComponent
                                className="mt-3 admin-btn-green"
                                onClick={() => this.handleSave()}
                                color="secondary"
                                value={!isLoading ? "Update" : "Loading..."}
                                disabled={isLoading}
                                style={{ minWidth: "auto" }}
                              />
                            )}

                            <ButtonComponent
                              onClick={this.toggleInputModal}
                              className="mr-lr-30 btn-back"
                              value="Back"
                            />
                          </div>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                )}
              </div>
            </Modal>

            <Modal
              className="news-modal"
              open={isActivityModalOpen}
              onClose={() => this.toggleActivityModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">User Activities</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleActivityModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <StatisticsTableModal userId={userId} />
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="news-modal file-upload-modal"
              open={isImportModalOpen}
              onClose={() => this.toggleImportModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">Import CSV File</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleImportModal()}
                />

                <Grid
                  container
                  className="page-content adminLogin text-left d-flex"
                  xs={12}
                >
                  <Grid
                    container
                    item
                    xs={12}
                    className="pageWrapper api-provider modal-items"
                  >
                    <Dropzone
                      accept=".csv, .xlsx, .xls"
                      multiple={false}
                      onDrop={this.handleFileDrop}
                    >
                      {({ getRootProps, getInputProps }) => (
                        <div className="file-uploader">
                          <div
                            {...getRootProps({
                              className:
                                "dropzone d-flex justify-content-center align-items-center",
                            })}
                          >
                            <input {...getInputProps()} />
                            <p>
                              Drag 'n' drop a file here, or click to select a
                              file
                            </p>
                          </div>
                        </div>
                      )}
                    </Dropzone>
                    {selectedFile && (
                      <div>
                        <p>Selected File: {selectedFile.name}</p>
                        {/* You can render more details about the file here */}
                      </div>
                    )}
                  </Grid>
                  <Grid
                    container
                    item
                    xs={6}
                    className="pageWrapper api-provider modal-items"
                  >
                    <ButtonComponent
                      className="admin-btn-green min-w ml-20"
                      onClick={this.handleUpload}
                      color="primary"
                      value={!isLoading ? "Upload" : "Loading..."}
                      disabled={!Boolean(selectedFile?.path) || isLoading}
                    />
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="news-modal file-upload-modal"
              open={isImportUserDetailsModalOpen}
              onClose={() => this.toggleImportUserDetailsModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">Import User Status</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleImportUserDetailsModal()}
                />

                <Grid
                  container
                  className="page-content adminLogin text-left d-flex"
                  xs={12}
                >
                  <Grid
                    container
                    item
                    xs={12}
                    className="pageWrapper api-provider modal-items import-user-details"
                  >
                    {importDetails?.alreadyExistMails?.length > 0 ? (
                      <Box className="details-status-wrap">
                        <Box className="label">already Exist Mails</Box>
                        {importDetails?.alreadyExistMails?.map(
                          (item, index) => {
                            return (
                              <>
                                <Box className="email-details" key={index}>
                                  <Box className="email">{item?.email}</Box>
                                  <Box className="reason warning">
                                    {item?.reason}
                                  </Box>
                                </Box>
                              </>
                            );
                          }
                        )}
                      </Box>
                    ) : (
                      <></>
                    )}
                    {importDetails?.faild?.length > 0 ? (
                      <Box className="details-status-wrap">
                        <Box className="label">faild</Box>
                        {importDetails?.faild?.map((item, index) => {
                          return (
                            <>
                              <Box className="email-details" key={index}>
                                <Box className="email">{item?.email}</Box>
                                <Box className="reason faild">
                                  {item?.reason}
                                </Box>
                              </Box>
                            </>
                          );
                        })}
                      </Box>
                    ) : (
                      <></>
                    )}
                    {importDetails?.insertedUser?.length > 0 ? (
                      <Box className="details-status-wrap">
                        <Box className="label">inserted User</Box>
                        {importDetails?.insertedUser?.map((item, index) => {
                          return (
                            <>
                              <Box className="email-details" key={index}>
                                <Box className="email">{item?.username}</Box>
                                <Box className="reason success">
                                  Users added successfully.
                                </Box>
                              </Box>
                            </>
                          );
                        })}
                      </Box>
                    ) : (
                      <></>
                    )}
                    {importDetails?.repeated?.length > 0 ? (
                      <Box className="details-status-wrap">
                        <Box className="label">repeated</Box>
                        {importDetails?.repeated?.map((item, index) => {
                          return (
                            <>
                              <Box className="email-details" key={index}>
                                <Box className="email">{item?.email}</Box>
                                <Box className="reason warning">
                                  {item?.reason}
                                </Box>
                              </Box>
                            </>
                          );
                        })}
                      </Box>
                    ) : (
                      <></>
                    )}
                  </Grid>
                  <Grid
                    container
                    item
                    xs={6}
                    className="pageWrapper api-provider modal-items"
                  >
                    <ButtonComponent
                      className="admin-btn-green min-w ml-20"
                      onClick={this.toggleImportUserDetailsModal}
                      color="primary"
                      value={"Back"}
                      // disabled={!Boolean(selectedFile?.path) || isLoading}
                    />
                  </Grid>
                </Grid>
              </div>
            </Modal>

            {/* <Modal
            className="modal modal-input"
            open={isImportModalOpen}
            onClose={() => this.toggleImportModal()}
          >
            <div
              className={"paper modal-show-scroll"}
              style={{ position: "relative" }}
            >
              <h3 className="text-center">Import CSV File</h3>
              <CancelIcon
                className="admin-close-icon"
                onClick={this.toggleImportModal()}
              />
              <Grid
                container
                className="page-content adminLogin text-left"
              >
                <Grid
                  item
                  xs={12}
                  className="pageWrapper api-provider"
                >
                  <Grid container>
                    <Grid
                      item
                      xs={6}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Upload your file{" "}
                      </label>
                      <input
                        type="file"
                        accept=".csv"
                        onChange={this.handleFileChange}
                      />
                    </Grid>
                    <Grid
                      item
                      xs={6}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    ></Grid>
                  </Grid>
                  <Grid container>
                    <Grid
                      item
                      xs={3}
                    >
                      <ButtonComponent
                        className="admin-btn-green min-w"
                        onClick={this.handleUpload}
                        color="primary"
                        value={!isLoading ? "Upload" : "Loading..."}
                        disabled={isLoading}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </div>
          </Modal> */}
            <Modal
              className="news-modal"
              open={isDetailsModalOpen}
              onClose={() => this.toggleDetailsModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">User Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleDetailsModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <DetailsTableModal
                      userId={userId}
                      userDetail={userDetail}
                    />
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="news-modal bank-details-modal"
              open={isBankDetailsModalOpen}
              onClose={() => this.toggleBankDetailsModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">Bank Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleBankDetailsModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {isBankDetailsLoader ? (
                      <Box className="flex justify-center">
                        <Loader />
                      </Box>
                    ) : bankDetails ? (
                      <Box>
                        <Box className="flex mb-8">
                          <Typography className="text-upper-user" variant="h6">
                            Account Holder Name:
                          </Typography>
                          <Typography className="ml-10 font-size125">
                            {bankDetails?.accountHolderName}
                          </Typography>
                        </Box>
                        <Box className="flex mb-8">
                          <Typography className="text-upper-user" variant="h6">
                            Account Number:
                          </Typography>
                          <Typography className="ml-10 font-size125">
                            {bankDetails?.accountNumber}
                          </Typography>
                        </Box>
                        <Box className="flex mb-8">
                          <Typography className="text-upper-user" variant="h6">
                            BSB:
                          </Typography>
                          <Typography className="ml-10 font-size125">
                            {bankDetails?.bsb}
                          </Typography>
                        </Box>
                        <Box className="flex">
                          <Typography className="text-upper-user" variant="h6">
                            Bank Name:
                          </Typography>
                          <Typography className="ml-10 font-size125">
                            {bankDetails?.bankName}
                          </Typography>
                        </Box>
                      </Box>
                    ) : (
                      <Box
                        className="modal-padding"
                        style={{ textAlign: "center" }}
                      >
                        <Typography>{bankDetailsMessage}</Typography>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="news-modal"
              open={isReferralDetailsModalOpen}
              onClose={() => this.toggleReferralDetailsModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">User Referral Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleReferralDetailsModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <ReferralDetail
                      userIdReferral={userIdReferral}
                      userDetailReferral={userDetailReferral}
                    />
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Users;
