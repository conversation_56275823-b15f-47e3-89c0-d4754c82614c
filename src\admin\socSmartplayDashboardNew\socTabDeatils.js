import { Box } from "@mui/material";
import React, { useState, useMemo, useEffect } from "react";
import CardSection from "./cardSection";
import ChartCard from "./chartCard";

const cardData = [
  {
    title: "Current Paying Subscribers",
    value: 300,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "New Subscribers (last 30 days)",
    value: 100,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Subscription Renewals",
    value: 100,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "decrease",
  },
  {
    title: "Subscription Cancellations",
    value: 33,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Payment",
    value: 10284,
    unit: null,
    currency: "USD",
    change: 13.3,
    changeType: "increase",
  },
];

// ---------- sample data generation ----------
const generateYearData = (year) => {
  const data = {};
  const currentDate = new Date();
  const currentYear = year;
  const monthsToGenerate =
    currentDate.getFullYear() === currentYear ? currentDate.getMonth() + 1 : 12;

  for (let month = 0; month < monthsToGenerate; month++) {
    const monthKey = month + 1;
    data[monthKey] = {};
    const daysInMonth = new Date(currentYear, month + 1, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const dateObj = new Date(currentYear, month, day);
      if (dateObj <= currentDate) {
        const dateKey = `${currentYear}-${String(month + 1).padStart(
          2,
          "0"
        )}-${String(day).padStart(2, "0")}`;
        data[monthKey][dateKey] = {
          date: dateKey,
          paidUsers: Math.floor(Math.random() * 100) + 50,
          freeTrial: Math.floor(Math.random() * 80) + 40,
          newSubscribers: Math.floor(Math.random() * 50) + 20,
          payment: Math.floor(Math.random() * 2000) + 500,
          renewals: Math.floor(Math.random() * 60) + 30,
          cancellations: Math.floor(Math.random() * 30) + 10,
          dayName: dateObj.toLocaleDateString("en-US", { weekday: "short" }),
        };
      }
    }
  }
  return data;
};

const generateRecentData = (days) => {
  const data = [];
  const currentDate = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(currentDate);
    date.setDate(currentDate.getDate() - i);

    data.push({
      date: date.toISOString().split("T")[0],
      paidUsers: Math.floor(Math.random() * 100) + 50,
      freeTrial: Math.floor(Math.random() * 80) + 40,
      newSubscribers: Math.floor(Math.random() * 50) + 20,
      dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
    });
  }

  return data;
};
// --------------------------------------------

const SOCTabDeatils = ({ selectedYear }) => {
  // Helper function to get the default month based on selected year
  const getDefaultMonth = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11, so add 1

    // If selected year is current year, return current month, otherwise return 1 (January)
    return selectedYear === currentYear ? currentMonth : 1;
  };

  const [currentPayingMonth, setCurrentPayingMonth] = useState(getDefaultMonth());
  const [currentPayingWeek, setCurrentPayingWeek] = useState(0);

  const [newSubsFilter, setNewSubsFilter] = useState("Last 7 days");

  const [paymentMonth, setPaymentMonth] = useState(getDefaultMonth());
  const [paymentWeek, setPaymentWeek] = useState(0);

  const [renewalsMonth, setRenewalsMonth] = useState(getDefaultMonth());
  const [renewalsWeek, setRenewalsWeek] = useState(0);

  const [cancellationsMonth, setCancellationsMonth] = useState(getDefaultMonth());
  const [cancellationsWeek, setCancellationsWeek] = useState(0);

  // Effect to update all month filters when selectedYear changes
  useEffect(() => {
    const defaultMonth = getDefaultMonth();
    setCurrentPayingMonth(defaultMonth);
    setPaymentMonth(defaultMonth);
    setRenewalsMonth(defaultMonth);
    setCancellationsMonth(defaultMonth);

    // Reset all week selections to 0 when year changes
    setCurrentPayingWeek(0);
    setPaymentWeek(0);
    setRenewalsWeek(0);
    setCancellationsWeek(0);
  }, [selectedYear]);

  const yearData = useMemo(() => generateYearData(selectedYear), [selectedYear]);

  const getWeeks = (month) => {
    const monthData = yearData[month] ?? {};
    const dates = Object.keys(monthData).sort();
    const weeks = [];

    if (dates.length === 0) return weeks;

    const firstDate = new Date(dates[0]);
    const startOfWeek = new Date(firstDate);
    startOfWeek.setDate(firstDate.getDate() - firstDate.getDay());

    const currentWeekStart = new Date(startOfWeek);

    while (currentWeekStart <= new Date(dates[dates.length - 1])) {
      const week = [];

      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(currentWeekStart);
        currentDate.setDate(currentWeekStart.getDate() + i);

        const dateKey = `${currentDate.getFullYear()}-${String(
          currentDate.getMonth() + 1
        ).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`;

        if (monthData[dateKey]) {
          week.push(monthData[dateKey]);
        } else {
          week.push({
            date: dateKey,
            paidUsers: 0,
            freeTrial: 0,
            newSubscribers: 0,
            payment: 0,
            renewals: 0,
            cancellations: 0,
            dayName: currentDate.toLocaleDateString("en-US", {
              weekday: "short",
            }),
            hasData: false,
          });
        }
      }

      weeks.push(week);
      currentWeekStart.setDate(currentWeekStart.getDate() + 7);
    }

    return weeks;
  };

  // Current Paying Subscribers Data
  const currentPayingWeeks = getWeeks(currentPayingMonth);

  const currentPayingData = (currentPayingWeeks[currentPayingWeek] ?? []).map(
    (d) => ({
      name: d.dayName,
      paidUsers: d.hasData === false ? 0 : d.paidUsers,
      freeTrial: d.hasData === false ? 0 : d.freeTrial,
      date: d.date,
      hasData: d.hasData !== false,
    })
  );
  const currentPayingTotal = currentPayingData.reduce(
    (s, d) => s + (d.hasData ? d.paidUsers : 0),
    0
  );

  // New Subscribers Data
  const newSubsData = useMemo(() => {
    const days = newSubsFilter === "Last 7 days" ? 7 : 30;
    const recentData = generateRecentData(days);
    return recentData.map((d) => ({
      name: d.dayName,
      paidUsers: d.paidUsers,
      freeTrial: d.freeTrial,
      date: d.date,
      hasData: true,
    }));
  }, [newSubsFilter]);
  const newSubsTotal = newSubsData.reduce((s, d) => s + d.paidUsers, 0);

  // Payment Data
  const paymentWeeks = getWeeks(paymentMonth);
  const paymentData = (paymentWeeks[paymentWeek] ?? []).map((d) => ({
    name: d.dayName,
    payment: d.hasData === false ? 0 : d.payment,
    date: d.date,
    hasData: d.hasData !== false,
  }));
  const paymentTotal = paymentData.reduce(
    (s, d) => s + (d.hasData ? d.payment : 0),
    0
  );

  // Renewals Data
  const renewalsWeeks = getWeeks(renewalsMonth);
  const renewalsData = (renewalsWeeks[renewalsWeek] ?? []).map((d) => ({
    name: d.dayName,
    renewals: d.hasData === false ? 0 : d.renewals,
    date: d.date,
    hasData: d.hasData !== false,
  }));
  const renewalsTotal = renewalsData.reduce(
    (s, d) => s + (d.hasData ? d.renewals : 0),
    0
  );

  // Cancellations Data
  const cancellationsWeeks = getWeeks(cancellationsMonth);
  const cancellationsData = (cancellationsWeeks[cancellationsWeek] ?? []).map(
    (d) => ({
      name: d.dayName,
      cancellations: d.hasData === false ? 0 : d.cancellations,
      date: d.date,
      hasData: d.hasData !== false,
    })
  );
  const cancellationsTotal = cancellationsData.reduce(
    (s, d) => s + (d.hasData ? d.cancellations : 0),
    0
  );

  return (
    <>
      <Box className="details-wrap">
        <Box className="card-section">
          {cardData?.map((item, index, arr) => {
            return (
              <CardSection
                itemData={item}
                itemIndex={index}
                fullData={arr}
                key={index}
              />
            );
          })}
        </Box>
        <Box className="bar-chart-section">
          <Box className="d-flex-ss align-items-center gap-18">
            {/* Current Paying Subscribers */}
            <Box className="w-50 bar-wrap-section">
              <ChartCard
                title="Current Paying Subscribers"
                value={currentPayingTotal}
                change="13.3"
                data={currentPayingData}
                dataKeys={["paidUsers", "freeTrial"]}
                colors={["#4f46e5", "#fb7185"]}
                selectedMonth={currentPayingMonth}
                onMonthChange={setCurrentPayingMonth}
                weekIdx={currentPayingWeek}
                onWeekChange={setCurrentPayingWeek}
                maxWeeks={currentPayingWeeks.length}
                selectedYear={selectedYear}
              />
            </Box>
            {/* New Subscribers (last 7 days) */}
            <Box className="w-50 bar-wrap-section">
              <ChartCard
                title="New Subscribers (last 7 days)"
                value={newSubsTotal}
                change="13.3"
                data={newSubsData}
                dataKeys={["paidUsers", "freeTrial"]}
                colors={["#4f46e5", "#fb7185"]}
                hasSpecialFilter={true}
                specialFilterValue={newSubsFilter}
                onSpecialFilterChange={setNewSubsFilter}
                hasWeekNav={false}
              />
            </Box>
          </Box>
          {/* Payment */}
          <Box className="bar-wrap-section mt-18 mb-18">
            <ChartCard
              title="Payment"
              value={paymentTotal}
              change="13.3"
              data={paymentData}
              dataKeys={["payment"]}
              colors={["#fb7185"]}
              selectedMonth={paymentMonth}
              onMonthChange={setPaymentMonth}
              weekIdx={paymentWeek}
              onWeekChange={setPaymentWeek}
              maxWeeks={paymentWeeks.length}
              prefix="$"
              selectedYear={selectedYear}
            />
          </Box>
          <Box className="d-flex-ss align-items-center gap-18">
            {/* Subscription Renewals */}
            <Box className="w-50 bar-wrap-section">
              <ChartCard
                title="Subscription Renewals"
                value={renewalsTotal}
                change="13.3"
                data={renewalsData}
                dataKeys={["renewals"]}
                colors={["#10b981"]}
                selectedMonth={renewalsMonth}
                onMonthChange={setRenewalsMonth}
                weekIdx={renewalsWeek}
                onWeekChange={setRenewalsWeek}
                maxWeeks={renewalsWeeks.length}
                selectedYear={selectedYear}
              />
            </Box>
            {/* Subscription Cancellations */}
            <Box className="w-50 bar-wrap-section">
              <ChartCard
                title="Subscription Cancellations"
                value={cancellationsTotal}
                change="13.3"
                data={cancellationsData}
                dataKeys={["cancellations"]}
                colors={["#f97316"]}
                selectedMonth={cancellationsMonth}
                onMonthChange={setCancellationsMonth}
                weekIdx={cancellationsWeek}
                onWeekChange={setCancellationsWeek}
                maxWeeks={cancellationsWeeks.length}
                selectedYear={selectedYear}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SOCTabDeatils;
